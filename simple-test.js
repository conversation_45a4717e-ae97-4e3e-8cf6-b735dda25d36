/**
 * Simple test script to verify backend implementation
 */

const BASE_URL = "http://localhost:3000";

async function testEndpoints() {
  console.log("🧪 Testing Backend Implementation...\n");

  try {
    // Test 1: Try to register a user
    console.log("👤 Test 1: Registering test user...");
    const registerResponse = await fetch(`${BASE_URL}/api/users/register`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        username: "testuser",
        email: "<EMAIL>",
        password: "testpassword123",
        first_name: "Test",
        last_name: "User",
      }),
    });

    let token = null;

    if (registerResponse.ok) {
      const registerData = await registerResponse.json();
      console.log("✅ User registered successfully");
      // Registration doesn't return token, need to login
      console.log("ℹ️ Now logging in to get token...");
    } else {
      // User might already exist, try to login
      console.log("ℹ️ Registration failed (user might exist), trying login...");
    }

    // Always try to login to get token
    const loginResponse = await fetch(`${BASE_URL}/api/users/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        username: "<EMAIL>",
        password: "testpassword123",
      }),
    });

    if (loginResponse.ok) {
      const loginData = await loginResponse.json();
      token = loginData.token;
      console.log("✅ Login successful");
    } else {
      const error = await loginResponse.json();
      console.log("❌ Login failed:", error.message);
      return;
    }

    if (!token) {
      console.log("❌ Could not obtain authentication token");
      return;
    }

    const headers = {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    };

    // Test 2: Create DATE custom field
    console.log("\n📅 Test 2: Creating DATE custom field...");
    const dateFieldResponse = await fetch(`${BASE_URL}/api/workitems/custom-fields/`, {
      method: "POST",
      headers,
      body: JSON.stringify({
        name: "Test Due Date",
        field_type: "DATE",
        is_required: false,
        sort_order: 1,
      }),
    });

    if (dateFieldResponse.ok) {
      const dateField = await dateFieldResponse.json();
      console.log("✅ DATE field created:", dateField.name, "- Type:", dateField.field_type);
    } else {
      const error = await dateFieldResponse.json();
      console.log("❌ DATE field creation failed:", error.message);
    }

    // Test 3: Create WEEK custom field
    console.log("\n📅 Test 3: Creating WEEK custom field...");
    const weekFieldResponse = await fetch(`${BASE_URL}/api/workitems/custom-fields/`, {
      method: "POST",
      headers,
      body: JSON.stringify({
        name: "Test Target Week",
        field_type: "WEEK",
        is_required: false,
        sort_order: 2,
      }),
    });

    if (weekFieldResponse.ok) {
      const weekField = await weekFieldResponse.json();
      console.log("✅ WEEK field created:", weekField.name, "- Type:", weekField.field_type);
    } else {
      const error = await weekFieldResponse.json();
      console.log("❌ WEEK field creation failed:", error.message);
    }

    // Test 4: Set week_starts_on preference
    console.log("\n⚙️ Test 4: Setting week_starts_on preference...");
    const preferencesResponse = await fetch(`${BASE_URL}/api/users/me/preferences/`, {
      method: "PATCH",
      headers,
      body: JSON.stringify({
        week_starts_on: "Monday",
      }),
    });

    if (preferencesResponse.ok) {
      const preferences = await preferencesResponse.json();
      console.log("✅ Preferences updated. week_starts_on:", preferences.week_starts_on);
    } else {
      const error = await preferencesResponse.json();
      console.log("❌ Preferences update failed:", error.message);
    }

    // Test 5: Get preferences to verify
    console.log("\n📋 Test 5: Getting preferences...");
    const getPreferencesResponse = await fetch(`${BASE_URL}/api/users/me/preferences/`, {
      method: "GET",
      headers,
    });

    if (getPreferencesResponse.ok) {
      const preferences = await getPreferencesResponse.json();
      console.log("✅ Retrieved preferences. week_starts_on:", preferences.week_starts_on);
    } else {
      const error = await getPreferencesResponse.json();
      console.log("❌ Get preferences failed:", error.message);
    }

    // Test 6: Test invalid week_starts_on value
    console.log("\n❌ Test 6: Testing invalid week_starts_on value...");
    const invalidResponse = await fetch(`${BASE_URL}/api/users/me/preferences/`, {
      method: "PATCH",
      headers,
      body: JSON.stringify({
        week_starts_on: "Tuesday",
      }),
    });

    if (!invalidResponse.ok) {
      const error = await invalidResponse.json();
      console.log("✅ Validation working correctly. Error:", error.message);
    } else {
      console.log("❌ Validation failed - invalid value was accepted");
    }

    console.log("\n🎉 Backend testing completed!");
  } catch (error) {
    console.error("❌ Test failed with error:", error.message);
  }
}

// Run the tests
testEndpoints();
