"use client";

import React, { useState } from "react";
import { Box, Typography, Paper, Container } from "@mui/material";
import WeekPicker from "@/components/forms/WeekPicker";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { apiDateToDateObject, dateObjectToApiFormat } from "@/lib/utils/dateUtils";

const TestComponentsPage: React.FC = () => {
  const [weekValue, setWeekValue] = useState<string | null>(null);
  const [dateValue, setDateValue] = useState<string | null>(null);

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Component Testing Page
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        This page is for testing the DatePicker and WeekPicker components.
      </Typography>

      <Box sx={{ display: "flex", flexDirection: "column", gap: 4 }}>
        {/* DatePicker Test */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            DatePicker Component
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Format: "E, d MMM, yy" (e.g., "Mon, 16 Jun, 25")
          </Typography>
          
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="Test Date Field"
              value={apiDateToDateObject(dateValue)}
              onChange={(date) => setDateValue(dateObjectToApiFormat(date))}
              format="E, d MMM, yy"
              slotProps={{
                textField: {
                  fullWidth: true,
                  size: "small",
                  placeholder: "Mon, 16 Jun, 25",
                },
              }}
            />
          </LocalizationProvider>
          
          <Typography variant="body2" sx={{ mt: 2 }}>
            <strong>Current Value:</strong> {dateValue || "null"}
          </Typography>
        </Paper>

        {/* WeekPicker Test */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            WeekPicker Component
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Format: "W&lt;Num&gt;: &lt;Day&gt; &lt;Month&gt; - &lt;Day&gt; &lt;Month&gt;" (e.g., "W25: 15 Jun - 21 Jun")
          </Typography>
          
          <WeekPicker
            label="Test Week Field"
            value={weekValue}
            onChange={setWeekValue}
            fullWidth
            size="small"
          />
          
          <Typography variant="body2" sx={{ mt: 2 }}>
            <strong>Current Value:</strong> {weekValue || "null"}
          </Typography>
        </Paper>

        {/* Features Test */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            WeekPicker Features to Test
          </Typography>
          <Box component="ul" sx={{ pl: 2 }}>
            <li>Click the WeekPicker input to open calendar popover</li>
            <li>Check week numbers displayed in left column</li>
            <li>Verify current week is highlighted</li>
            <li>Hover over days to see entire week highlighted</li>
            <li>Click any day to select that week</li>
            <li>Check if week starts on Sunday or Monday based on user preference</li>
            <li>Use navigation arrows to change months</li>
            <li>Test clear button (X) when a week is selected</li>
          </Box>
        </Paper>

        {/* User Preference Test */}
        <Paper sx={{ p: 3 }}>
          <Typography variant="h6" gutterBottom>
            User Preference Integration
          </Typography>
          <Typography variant="body2" color="text.secondary">
            The WeekPicker automatically fetches the user's week_starts_on preference from the API.
            You can change this preference via the API and refresh the page to see the effect.
          </Typography>
          <Typography variant="body2" sx={{ mt: 1 }}>
            <strong>API Endpoint:</strong> PATCH /api/users/me/preferences/
          </Typography>
          <Typography variant="body2">
            <strong>Payload:</strong> {`{ "week_starts_on": "Monday" }`} or {`{ "week_starts_on": "Sunday" }`}
          </Typography>
        </Paper>
      </Box>
    </Container>
  );
};

export default TestComponentsPage;
