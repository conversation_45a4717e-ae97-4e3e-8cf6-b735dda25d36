# Manual Testing Commands for Backend API

## Prerequisites
1. Make sure the development server is running: `npm run dev`
2. You need valid user credentials to test with

## Step 1: Create a Test User (if needed)
```bash
curl -X POST http://localhost:3000/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "testpassword123",
    "first_name": "Test",
    "last_name": "User"
  }'
```

## Step 2: Login to Get JWT Token
```bash
curl -X POST http://localhost:3000/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "<EMAIL>",
    "password": "testpassword123"
  }'
```

**Save the token from the response for the next steps!**

## Step 3: Test Creating DATE Custom Field
```bash
curl -X POST http://localhost:3000/api/workitems/custom-fields/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -d '{
    "name": "Due Date",
    "field_type": "DATE",
    "is_required": false,
    "sort_order": 1
  }'
```

## Step 4: Test Creating WEEK Custom Field
```bash
curl -X POST http://localhost:3000/api/workitems/custom-fields/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -d '{
    "name": "Target Week",
    "field_type": "WEEK",
    "is_required": false,
    "sort_order": 2
  }'
```

## Step 5: Test Setting week_starts_on Preference
```bash
curl -X PATCH http://localhost:3000/api/users/me/preferences/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -d '{
    "week_starts_on": "Monday"
  }'
```

## Step 6: Test Getting Preferences
```bash
curl -X GET http://localhost:3000/api/users/me/preferences/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

## Step 7: Test Invalid week_starts_on Value (Should Fail)
```bash
curl -X PATCH http://localhost:3000/api/users/me/preferences/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -d '{
    "week_starts_on": "Tuesday"
  }'
```

## Expected Results

### DATE Field Creation
- Should return 201 status with field definition
- field_type should be "DATE"

### WEEK Field Creation  
- Should return 201 status with field definition
- field_type should be "WEEK"

### week_starts_on Preference
- PATCH with "Monday" should return 200 with updated preferences
- GET should return preferences with week_starts_on: "Monday"
- PATCH with "Tuesday" should return 400 with validation error

## Validation Testing

### Test DATE Field Value Validation
Create a project with DATE field (replace FIELD_ID with actual field ID):
```bash
curl -X POST http://localhost:3000/api/workitems/projects \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -d '{
    "name": "Test Project",
    "life_aspect": "LIFE_ASPECT_ID_HERE",
    "custom_field_inputs": [
      {
        "definition_id": "DATE_FIELD_ID_HERE",
        "value": "2025-06-17"
      }
    ]
  }'
```

### Test WEEK Field Value Validation
```bash
curl -X POST http://localhost:3000/api/workitems/projects \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -d '{
    "name": "Test Project 2",
    "life_aspect": "LIFE_ASPECT_ID_HERE", 
    "custom_field_inputs": [
      {
        "definition_id": "WEEK_FIELD_ID_HERE",
        "value": "2025-W25"
      }
    ]
  }'
```
