import { prisma } from "@/lib/database/prisma";
import { ResolvedCustomField, CustomFieldInput } from "@/lib/types/customFields";
import { formatDateProfessional } from "@/lib/utils/dateUtils";

// Resolve custom field values to rich objects for API responses
export async function resolveCustomFields(
  customFieldValues: Record<string, any>,
  targetModel: "PROJECT" | "OUTCOME" | "WEEKLY_PLAN_FOCUS",
  userId: string
): Promise<ResolvedCustomField[]> {
  if (!customFieldValues || Object.keys(customFieldValues).length === 0) {
    return [];
  }

  // Get all custom field definitions for this user
  const definitionIds = Object.keys(customFieldValues);

  const definitions = await prisma.workitems_custom_field_definition.findMany({
    where: {
      user_id: userId,
      id: { in: definitionIds },
    },
    include: {
      choice_options: {
        orderBy: { sort_order: "asc" },
      },
    },
    orderBy: { sort_order: "asc" },
  });

  const resolvedFields: ResolvedCustomField[] = [];

  for (const definition of definitions) {
    const value = customFieldValues[definition.id];

    if (value === undefined || value === null) {
      continue;
    }

    let resolvedValue = value;
    let displayValue = String(value);

    // Handle choice fields
    if (definition.field_type === "SINGLE_SELECT" && value) {
      const choiceOption = definition.choice_options.find((opt) => opt.id === value);
      if (choiceOption) {
        resolvedValue = {
          id: choiceOption.id,
          value: choiceOption.value,
          color: choiceOption.color,
        };
        displayValue = choiceOption.value;
      }
    } else if (definition.field_type === "MULTI_SELECT" && Array.isArray(value)) {
      const choiceOptions = definition.choice_options.filter((opt) => value.includes(opt.id));
      resolvedValue = choiceOptions.map((opt) => ({
        id: opt.id,
        value: opt.value,
        color: opt.color,
      }));
      displayValue = choiceOptions.map((opt) => opt.value).join(", ");
    } else if (definition.field_type === "BOOLEAN") {
      displayValue = value ? "Yes" : "No";
    } else if (definition.field_type === "DATE") {
      displayValue = formatDateProfessional(value);
    } else if (definition.field_type === "DATETIME") {
      // For DATETIME, show both date and time in professional format
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        displayValue = formatDateProfessional(date) + " " + date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
      } else {
        displayValue = String(value);
      }
    } else if (definition.field_type === "NUMBER") {
      displayValue = typeof value === "number" ? value.toLocaleString() : String(value);
    }

    const resolvedField: ResolvedCustomField = {
      definition_id: definition.id,
      definition_name: definition.name,
      field_type: definition.field_type,
      value: resolvedValue,
      display_value: displayValue,
      is_required: definition.is_required,
      sort_order: definition.sort_order,
    };

    // Add choice_option for SINGLE_SELECT fields
    if (definition.field_type === "SINGLE_SELECT" && value) {
      const choiceOption = definition.choice_options.find((opt) => opt.id === value);
      if (choiceOption) {
        resolvedField.choice_option = {
          id: choiceOption.id,
          value: choiceOption.value,
          color: choiceOption.color,
          sort_order: choiceOption.sort_order,
          created_at: choiceOption.created_at,
          updated_at: choiceOption.updated_at,
        };
      }
    }

    // Add choice_options for MULTI_SELECT fields
    if (definition.field_type === "MULTI_SELECT" && Array.isArray(value)) {
      const choiceOptions = definition.choice_options.filter((opt) => value.includes(opt.id));
      resolvedField.choice_options = choiceOptions.map((opt) => ({
        id: opt.id,
        value: opt.value,
        color: opt.color,
        sort_order: opt.sort_order,
        created_at: opt.created_at,
        updated_at: opt.updated_at,
      }));
    }

    resolvedFields.push(resolvedField);
  }

  return resolvedFields;
}

// Process custom field inputs and store them in the custom_field_values JSONB field
export async function processCustomFieldInputs(
  customFieldInputs: CustomFieldInput[],
  targetModel: "PROJECT" | "OUTCOME" | "WEEKLY_PLAN_FOCUS",
  userId: string,
  existingValues: Record<string, any> = {}
): Promise<Record<string, any>> {
  if (!customFieldInputs || customFieldInputs.length === 0) {
    return existingValues;
  }

  // BUGFIX: Filter out hardcoded static fields (start_date, end_date) that should not be processed as custom fields
  const filteredCustomFieldInputs = customFieldInputs.filter((input) => {
    return input.definition_id !== "start_date" && input.definition_id !== "end_date";
  });

  if (filteredCustomFieldInputs.length === 0) {
    return existingValues;
  }

  // Get all custom field definitions for validation
  const definitionIds = filteredCustomFieldInputs.map((input) => input.definition_id);
  const definitions = await prisma.workitems_custom_field_definition.findMany({
    where: {
      user_id: userId,
      id: { in: definitionIds },
    },
    include: {
      choice_options: true,
    },
  });

  const definitionMap = new Map(definitions.map((def) => [def.id, def]));
  const updatedValues = { ...existingValues };

  for (const input of filteredCustomFieldInputs) {
    const definition = definitionMap.get(input.definition_id);
    if (!definition) {
      throw new Error(`Custom field definition not found: ${input.definition_id}`);
    }

    // Validate and process the value based on field type
    let processedValue = input.value;

    if (input.value === null || input.value === undefined) {
      // Clear the field
      delete updatedValues[input.definition_id];
      continue;
    }

    // Type-specific validation and processing
    switch (definition.field_type) {
      case "TEXT":
      case "TEXTAREA":
      case "EMAIL":
      case "URL":
      case "PHONE":
        processedValue = String(input.value);
        break;

      case "NUMBER":
        processedValue = Number(input.value);
        if (isNaN(processedValue)) {
          throw new Error(`Invalid number value for field ${definition.name}`);
        }
        break;

      case "BOOLEAN":
        processedValue = Boolean(input.value);
        break;

      case "DATE":
        // Accept various date formats and convert to YYYY-MM-DD format
        const dateValue = new Date(input.value);
        if (isNaN(dateValue.getTime())) {
          throw new Error(`Invalid date value for field ${definition.name}`);
        }
        // Store dates in YYYY-MM-DD format
        processedValue = dateValue.toISOString().split("T")[0];
        break;

      case "DATETIME":
        // Accept various date formats and convert to ISO string
        const datetimeValue = new Date(input.value);
        if (isNaN(datetimeValue.getTime())) {
          throw new Error(`Invalid datetime value for field ${definition.name}`);
        }
        processedValue = datetimeValue.toISOString();
        break;

      case "WEEK":
        // Validate WEEK format (YYYY-WNN)
        const weekRegex = /^\d{4}-W(0[1-9]|[1-4][0-9]|5[0-3])$/;
        if (!weekRegex.test(String(input.value))) {
          throw new Error(`Invalid week value for field ${definition.name}. Must be in YYYY-WNN format (e.g., 2025-W25)`);
        }
        processedValue = String(input.value);
        break;

      case "SINGLE_SELECT":
        // Add debug logging
        console.log(`🔍 Processing SINGLE_SELECT field "${definition.name}":`, {
          fieldId: definition.id,
          inputValue: input.value,
          valueType: typeof input.value,
          availableOptions: definition.choice_options.map((opt) => ({ id: opt.id, value: opt.value })),
        });

        // Handle null/undefined values
        if (input.value === null || input.value === undefined) {
          processedValue = null;
          break;
        }

        // Ensure the input value is a string
        const choiceId = typeof input.value === "object" && input.value !== null && "id" in input.value ? input.value.id : String(input.value);

        // Validate that the choice option exists
        const singleChoice = definition.choice_options.find((opt) => opt.id === choiceId);
        if (!singleChoice) {
          console.error(`❌ Invalid choice option for ${definition.name}:`, {
            attempted: choiceId,
            available: definition.choice_options.map((opt) => opt.id),
          });
          throw new Error(`Invalid choice option for field ${definition.name}`);
        }
        processedValue = choiceId;
        break;

      case "MULTI_SELECT":
        // Validate that all choice options exist
        if (!Array.isArray(input.value)) {
          throw new Error(`Multi-select field ${definition.name} requires an array value`);
        }
        const validChoices = definition.choice_options.map((opt) => opt.id);
        const invalidChoices = input.value.filter((val) => !validChoices.includes(val));
        if (invalidChoices.length > 0) {
          throw new Error(`Invalid choice options for field ${definition.name}: ${invalidChoices.join(", ")}`);
        }
        processedValue = input.value;
        break;

      default:
        throw new Error(`Unsupported field type: ${definition.field_type}`);
    }

    updatedValues[input.definition_id] = processedValue;
  }

  return updatedValues;
}
