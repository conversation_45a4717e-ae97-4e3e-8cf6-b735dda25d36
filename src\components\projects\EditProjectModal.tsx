"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  Divider,
  CircularProgress,
  IconButton,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import { projectsService } from "@/lib/api/projectsService";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { Project, UpdateProjectData } from "@/lib/types/projects";
import { CustomFieldDefinition, CustomFieldInput } from "@/lib/types/customFields";

import CustomFieldsForm from "./CustomFieldsForm";

interface EditProjectModalProps {
  open: boolean;
  projectId: string;
  onClose: () => void;
  onSuccess: () => void;
}

const EditProjectModal: React.FC<EditProjectModalProps> = ({ open, projectId, onClose, onSuccess }) => {
  // Form state
  const [projectName, setProjectName] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [customFieldInputs, setCustomFieldInputs] = useState<CustomFieldInput[]>([]);

  // Data state
  const [project, setProject] = useState<Project | null>(null);
  const [customFieldDefinitions, setCustomFieldDefinitions] = useState<CustomFieldDefinition[]>([]);

  // UI state
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load project data and custom field definitions
  useEffect(() => {
    if (open && projectId) {
      loadData();
    }
  }, [open, projectId]);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load project data and custom field definitions in parallel
      const [projectResponse, customFieldsResponse] = await Promise.all([
        projectsService.getProject(projectId),
        customFieldsService.getCustomFieldDefinitions(),
      ]);

      // Debug: Log the project data received from API
      console.log("🔍 DEBUG: Project data received from API:", {
        id: projectResponse.id,
        name: projectResponse.name,
        fullResponse: projectResponse,
      });

      setProject(projectResponse);
      setProjectName(projectResponse.name);
      setProjectDescription(projectResponse.description || "");

      // Set custom field definitions
      const definitions: CustomFieldDefinition[] = Array.isArray(customFieldsResponse)
        ? customFieldsResponse
        : (customFieldsResponse as any).results || [];

      // Debug logging for custom fields
      console.log("🔍 EditProjectModal - Custom Fields Response:", customFieldsResponse);
      console.log("🔍 EditProjectModal - Processed Definitions:", definitions);

      setCustomFieldDefinitions(definitions);

      // Initialize custom field inputs with current values
      const initialInputs: CustomFieldInput[] = definitions.map((definition: CustomFieldDefinition) => {
        const existingField = projectResponse.resolved_custom_fields?.find((field) => field.definition_id === definition.id);
        return {
          definition_id: definition.id,
          value: existingField?.value || null,
        };
      });
      setCustomFieldInputs(initialInputs);
    } catch (err) {
      console.error("Failed to load project data:", err);
      setError("Failed to load project data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSave = async () => {
    if (!project) return;

    setSaving(true);
    setError(null);

    try {
      // Process custom field inputs
      const processedCustomFieldInputs = customFieldInputs.map((input) => {
        // Ensure definition_id is a string (convert from number if needed)
        const stringDefinitionId = String(input.definition_id);

        return {
          ...input,
          definition_id: stringDefinitionId,
        };
      });

      // Debug: Log current state values before creating payload
      console.log("🔍 DEBUG: Current state values before save:", {
        projectName: projectName.trim(),
        description: projectDescription.trim(),
      });

      // Prepare update data with processed custom field inputs
      const updateData: UpdateProjectData = {
        name: projectName.trim(),
        description: projectDescription.trim() || undefined,
        custom_field_inputs: processedCustomFieldInputs,
      };

      // Log payload in development for verification
      console.log("📤 API Payload:", JSON.stringify(updateData, null, 2));

      // Update project
      await projectsService.updateProject(project.id, updateData);

      // Success - close modal and refresh data
      onSuccess();
    } catch (err: any) {
      console.error("Failed to update project:", err);
      setError(err.message || "Failed to update project. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!loading && !saving) {
      // Reset form state
      setProjectName("");
      setProjectDescription("");
      setCustomFieldInputs([]);
      setProject(null);
      setCustomFieldDefinitions([]);
      setError(null);
      onClose();
    }
  };

  // Handle custom fields change
  const handleCustomFieldsChange = (inputs: CustomFieldInput[]) => {
    // Debug logging to track state changes
    if (process.env.NODE_ENV === "development") {
      console.log(`🔍 DEBUG: Custom fields state updated:`, inputs);

      // Specifically log date fields
      const dateFields = inputs.filter((input) => {
        const definition = customFieldDefinitions.find((def) => def.id === input.definition_id);
        return definition && definition.field_type === "DATE";
      });

      if (dateFields.length > 0) {
        console.log(`📅 DEBUG: Date fields in state:`, dateFields);
      }
    }

    setCustomFieldInputs(inputs);
  };

  // Create merged current values that combines original resolved fields with any updates
  const mergedCurrentValues = React.useMemo(() => {
    if (!project?.resolved_custom_fields) return [];

    // Start with original resolved fields
    const merged = [...project.resolved_custom_fields];

    // Apply any updates from customFieldInputs
    customFieldInputs.forEach((input) => {
      const existingIndex = merged.findIndex((field) => field.definition_id === input.definition_id);
      if (existingIndex >= 0) {
        // Update existing field
        merged[existingIndex] = {
          ...merged[existingIndex],
          value: input.value,
        };
      } else {
        // Add new field (shouldn't happen in edit mode, but just in case)
        const definition = customFieldDefinitions.find((def) => def.id === input.definition_id);
        if (definition) {
          merged.push({
            definition_id: input.definition_id,
            definition_name: definition.name,
            field_type: definition.field_type,
            is_required: definition.is_required,
            sort_order: definition.sort_order,
            value: input.value,
            display_value: String(input.value || ""),
          });
        }
      }
    });

    return merged;
  }, [project?.resolved_custom_fields, customFieldInputs, customFieldDefinitions]);

  // Validation
  const isFormValid = projectName.trim().length > 0;
  const canSave = isFormValid && !loading && !saving;

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth slotProps={{ paper: { sx: { borderRadius: 2 } } }}>
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" component="h2">
            Edit Project
          </Typography>
          <IconButton onClick={handleClose} disabled={loading || saving} sx={{ p: 1 }}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" py={4}>
            <CircularProgress />
            <Typography variant="body2" sx={{ ml: 2 }}>
              Loading project data...
            </Typography>
          </Box>
        ) : (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            {/* Standard Project Fields */}
            <Box>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Project Details
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <TextField
                  label="Project Name"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  required
                  disabled={saving}
                  fullWidth
                  variant="outlined"
                  size="small"
                  error={!projectName.trim()}
                  helperText={!projectName.trim() ? "Project name is required" : ""}
                />
                <TextField
                  label="Description"
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  disabled={saving}
                  fullWidth
                  multiline
                  rows={3}
                  variant="outlined"
                  size="small"
                  placeholder="Optional project description..."
                />
              </Box>
            </Box>

            {/* Custom Fields - Always show section */}
            <Divider />
            <Box>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Custom Fields
              </Typography>
              {customFieldDefinitions.length > 0 ? (
                <CustomFieldsForm
                  customFieldDefinitions={customFieldDefinitions}
                  currentValues={mergedCurrentValues}
                  onChange={handleCustomFieldsChange}
                  disabled={saving}
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ fontStyle: "italic" }}>
                  No custom fields have been defined for projects yet. You can create custom fields in Settings.
                </Typography>
              )}
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={handleClose} disabled={loading || saving} color="inherit">
          Cancel
        </Button>
        <Button onClick={handleSave} disabled={!canSave} variant="contained" startIcon={saving ? <CircularProgress size={16} /> : undefined}>
          {saving ? "Saving..." : "Save Changes"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditProjectModal;
