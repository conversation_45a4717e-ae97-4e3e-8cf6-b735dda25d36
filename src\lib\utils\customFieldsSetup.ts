/**
 * Custom Fields Setup Utility
 * Handles automatic creation and management of custom field definitions
 */

import { customFieldsService } from "@/lib/api/customFieldsService";
import { projectsService } from "@/lib/api/projectsService";
import { CustomFieldDefinition, CreateCustomFieldDefinitionData } from "@/lib/types/customFields";

export interface CustomFieldSetupResult {
  success: boolean;
  message: string;
  definitions?: CustomFieldDefinition[];
  error?: any;
}

/**
 * Complete setup of custom fields for the application
 */
export const setupCustomFields = async (): Promise<CustomFieldSetupResult> => {
  try {
    console.log("🚀 Starting custom fields setup...");

    // Step 1: Ensure default custom field definitions exist
    const definitions = await customFieldsService.ensureDefaultProjectCustomFields();

    if (definitions.length === 0) {
      return {
        success: false,
        message: "Failed to create custom field definitions",
      };
    }

    console.log(
      "✅ Custom field definitions ready:",
      definitions.map((d) => d.name)
    );

    return {
      success: true,
      message: `Successfully set up ${definitions.length} custom field definitions`,
      definitions,
    };
  } catch (error) {
    console.error("❌ Custom fields setup failed:", error);
    return {
      success: false,
      message: "Failed to setup custom fields",
      error,
    };
  }
};

/**
 * Get custom field definition by name
 */
export const getCustomFieldDefinition = async (name: string): Promise<CustomFieldDefinition | null> => {
  try {
    const definitions = await customFieldsService.getProjectCustomFieldDefinitions();
    return definitions.find((def) => def.name === name) || null;
  } catch (error) {
    console.error(`Error getting custom field definition for ${name}:`, error);
    return null;
  }
};

/**
 * Assign priority to a project
 */
export const assignPriorityToProject = async (
  projectId: string,
  priorityValue: string
): Promise<{ success: boolean; message: string; project?: any }> => {
  try {
    console.log(`🎯 Assigning priority "${priorityValue}" to project ${projectId}...`);

    // Get the priority field definition
    console.log("📋 Fetching Priority Level custom field definition...");
    let priorityField = await getCustomFieldDefinition("Priority Level");
    console.log("📋 Priority field result:", priorityField);

    if (!priorityField) {
      console.log("❌ Priority Level custom field not found, attempting to set up custom fields...");

      // Try to set up custom fields automatically
      const setupResult = await setupCustomFields();
      if (!setupResult.success) {
        return {
          success: false,
          message: "Priority Level custom field not found and setup failed. Please run setup manually first.",
        };
      }

      // Try to get the field again after setup
      priorityField = await getCustomFieldDefinition("Priority Level");
      if (!priorityField) {
        return {
          success: false,
          message: "Priority Level custom field still not found after setup. Please check your configuration.",
        };
      }

      console.log("✅ Custom fields set up successfully, found Priority Level field");
    }

    // Find the priority option
    console.log("🔍 Available priority options:", priorityField.choice_options);
    const priorityOption = priorityField.choice_options.find((option) => option.value === priorityValue);
    console.log("🎯 Found priority option:", priorityOption);

    if (!priorityOption) {
      console.log("❌ Priority option not found");
      return {
        success: false,
        message: `Priority option "${priorityValue}" not found. Available options: ${priorityField.choice_options.map((o) => o.value).join(", ")}`,
      };
    }

    // Validate project exists before updating
    console.log("🔍 Checking if project exists...");
    try {
      await projectsService.getProject(projectId);
      console.log("✅ Project found");
    } catch (error) {
      console.log("❌ Project not found:", error);
      return {
        success: false,
        message: `Project with ID ${projectId} not found. Please check the project ID.`,
      };
    }

    // Update the project with custom field input
    console.log("📝 Updating project with custom field input:", {
      definition_id: priorityField.id,
      value: priorityOption.id,
    });

    const updatedProject = await projectsService.updateProject(projectId, {
      custom_field_inputs: [
        {
          definition_id: priorityField.id,
          value: priorityOption.id,
        },
      ],
    });

    console.log("✅ Project updated successfully:", updatedProject.name);

    return {
      success: true,
      message: `Successfully assigned "${priorityValue}" priority to project`,
      project: updatedProject,
    };
  } catch (error) {
    console.error("❌ Failed to assign priority:", error);

    // Log the error in different ways to debug
    console.error("Error type:", typeof error);
    console.error("Error constructor:", error?.constructor?.name);
    console.error("Error stringified:", JSON.stringify(error, null, 2));

    // Try to extract error details from axios error
    if (error && typeof error === "object" && "response" in error) {
      const axiosError = error as any;
      console.error("Axios error response:", axiosError.response?.data);
      console.error("Axios error status:", axiosError.response?.status);
      console.error("Axios error message:", axiosError.message);

      return {
        success: false,
        message: `Failed to assign priority: ${
          axiosError.response?.data?.detail || axiosError.response?.data?.message || axiosError.message || "Unknown error"
        }`,
      };
    }

    // Extract meaningful error message
    let errorMessage = "Failed to assign priority to project";
    if (error && typeof error === "object") {
      if ("message" in error && error.message) {
        errorMessage = `Failed to assign priority: ${error.message}`;
      } else if ("details" in error && error.details) {
        errorMessage = `Failed to assign priority: ${error.details}`;
      }
    } else if (typeof error === "string") {
      errorMessage = `Failed to assign priority: ${error}`;
    }

    return {
      success: false,
      message: errorMessage,
    };
  }
};

/**
 * Assign status to a project
 */
export const assignStatusToProject = async (
  projectId: string,
  statusValue: string
): Promise<{ success: boolean; message: string; project?: any }> => {
  try {
    console.log(`📊 Assigning status "${statusValue}" to project ${projectId}...`);

    // Get the status field definition
    const statusField = await getCustomFieldDefinition("Status");
    if (!statusField) {
      return {
        success: false,
        message: "Status custom field not found. Please run setup first.",
      };
    }

    // Find the status option
    const statusOption = statusField.choice_options.find((option) => option.value === statusValue);

    if (!statusOption) {
      return {
        success: false,
        message: `Status option "${statusValue}" not found. Available options: ${statusField.choice_options.map((o) => o.value).join(", ")}`,
      };
    }

    // Update the project with custom field input
    const updatedProject = await projectsService.updateProject(projectId, {
      custom_field_inputs: [
        {
          definition_id: statusField.id,
          value: statusOption.id,
        },
      ],
    });

    console.log("✅ Project status updated successfully:", updatedProject.name);

    return {
      success: true,
      message: `Successfully assigned "${statusValue}" status to project`,
      project: updatedProject,
    };
  } catch (error) {
    console.error("❌ Failed to assign status:", error);
    return {
      success: false,
      message: "Failed to assign status to project",
    };
  }
};

/**
 * Demo function to test custom fields with Hair Care project
 */
export const setupHairCareDemo = async (): Promise<{ success: boolean; message: string }> => {
  try {
    console.log("🧪 Setting up Hair Care demo with custom fields...");

    // First ensure custom fields are set up
    const setupResult = await setupCustomFields();
    if (!setupResult.success) {
      return setupResult;
    }

    // Hair Care project ID from your console log
    const hairCareProjectId = "9d714f07-62dd-408c-a605-c59d2659985e";

    // Assign High priority to Hair Care
    const priorityResult = await assignPriorityToProject(hairCareProjectId, "High");
    if (!priorityResult.success) {
      return priorityResult;
    }

    // Assign In Progress status to Hair Care
    const statusResult = await assignStatusToProject(hairCareProjectId, "In Progress");
    if (!statusResult.success) {
      return statusResult;
    }

    return {
      success: true,
      message: "✅ Hair Care demo setup complete! Priority and status assigned.",
    };
  } catch (error) {
    console.error("❌ Hair Care demo setup failed:", error);
    return {
      success: false,
      message: "Failed to setup Hair Care demo",
    };
  }
};

/**
 * Get available priority options
 */
export const getAvailablePriorityOptions = async (): Promise<string[]> => {
  try {
    const priorityField = await getCustomFieldDefinition("Priority Level");
    return priorityField?.choice_options.map((option) => option.value) || [];
  } catch (error) {
    console.error("Error getting priority options:", error);
    return [];
  }
};

/**
 * Get available status options
 */
export const getAvailableStatusOptions = async (): Promise<string[]> => {
  try {
    const statusField = await getCustomFieldDefinition("Status");
    return statusField?.choice_options.map((option) => option.value) || [];
  } catch (error) {
    console.error("Error getting status options:", error);
    return [];
  }
};

/**
 * Debug function to check custom fields setup
 */
export const debugCustomFields = async (): Promise<void> => {
  try {
    console.log("🔍 Debugging custom fields setup...");

    // Check if custom fields service is working
    console.log("📋 Fetching all custom field definitions...");
    const allDefinitions = await customFieldsService.getProjectCustomFieldDefinitions();
    console.log("📋 All custom field definitions:", allDefinitions);

    // Check specific fields
    const priorityField = await getCustomFieldDefinition("Priority Level");
    console.log("🎯 Priority Level field:", priorityField);

    const statusField = await getCustomFieldDefinition("Status");
    console.log("📊 Status field:", statusField);

    console.log("✅ Debug complete");
  } catch (error) {
    console.error("❌ Debug failed:", error);
  }
};
