"use client";

import React from "react";
import { Box, TextField, Select, MenuItem, FormControl, InputLabel, Checkbox, FormControlLabel, Chip, Typography, Paper } from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";

import { CustomFieldType, FIELD_TYPES } from "@/lib/types/customFields";
import WeekPicker from "@/components/forms/WeekPicker";

interface FieldTypePreviewProps {
  fieldType: CustomFieldType;
}

const FieldTypePreview: React.FC<FieldTypePreviewProps> = ({ fieldType }) => {
  const fieldTypeInfo = FIELD_TYPES.find((type) => type.type === fieldType);

  const renderPreview = () => {
    switch (fieldType) {
      case "TEXT":
        return <TextField fullWidth label="Sample Text Field" placeholder="Enter text here..." disabled size="small" />;

      case "TEXTAREA":
        return <TextField fullWidth label="Sample Text Area" placeholder="Enter multi-line text here..." multiline rows={3} disabled size="small" />;

      case "NUMBER":
        return <TextField fullWidth label="Sample Number Field" type="number" placeholder="Enter number..." disabled size="small" />;

      case "BOOLEAN":
        return <FormControlLabel control={<Checkbox disabled />} label="Sample Checkbox" />;

      case "DATE":
        return (
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DatePicker
              label="Sample Date Picker"
              disabled
              format="E, d MMM, yy"
              slotProps={{
                textField: {
                  fullWidth: true,
                  size: "small",
                  placeholder: "Mon, 16 Jun, 25",
                },
              }}
            />
          </LocalizationProvider>
        );

      case "DATETIME":
        return (
          <LocalizationProvider dateAdapter={AdapterDateFns}>
            <DateTimePicker
              label="Sample Date & Time Picker"
              disabled
              slotProps={{
                textField: {
                  fullWidth: true,
                  size: "small",
                },
              }}
            />
          </LocalizationProvider>
        );

      case "WEEK":
        return <WeekPicker label="Sample Week Field" value={null} onChange={() => {}} disabled fullWidth size="small" />;

      case "EMAIL":
        return <TextField fullWidth label="Sample Email Field" type="email" placeholder="<EMAIL>" disabled size="small" />;

      case "URL":
        return <TextField fullWidth label="Sample URL Field" type="url" placeholder="https://example.com" disabled size="small" />;

      case "PHONE":
        return <TextField fullWidth label="Sample Phone Field" type="tel" placeholder="+****************" disabled size="small" />;

      case "SINGLE_SELECT":
        return (
          <FormControl fullWidth size="small">
            <InputLabel>Sample Dropdown</InputLabel>
            <Select label="Sample Dropdown" disabled value="">
              <MenuItem value="option1">Option 1</MenuItem>
              <MenuItem value="option2">Option 2</MenuItem>
              <MenuItem value="option3">Option 3</MenuItem>
            </Select>
          </FormControl>
        );

      case "MULTI_SELECT":
        return (
          <Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
              Sample Multi-Select
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={0.5}>
              <Chip label="Option 1" size="small" color="primary" />
              <Chip label="Option 2" size="small" color="secondary" />
              <Chip label="Option 3" size="small" variant="outlined" />
            </Box>
          </Box>
        );

      default:
        return <TextField fullWidth label="Unknown Field Type" disabled size="small" />;
    }
  };

  return (
    <Paper
      variant="outlined"
      sx={{
        p: 2,
        backgroundColor: "grey.50",
        borderStyle: "dashed",
      }}
    >
      <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: "block" }}>
        Live Preview - {fieldTypeInfo?.label || fieldType}
      </Typography>

      <Box sx={{ opacity: 0.7 }}>{renderPreview()}</Box>

      {fieldTypeInfo && (
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: "block" }}>
          {fieldTypeInfo.description}
        </Typography>
      )}
    </Paper>
  );
};

export default FieldTypePreview;
