/**
 * Test script for backend API implementation
 * Tests DATE and WEEK custom fields, and week_starts_on preference
 */

const BASE_URL = "http://localhost:3000";

// Test credentials - you'll need to replace these with valid credentials
const TEST_CREDENTIALS = {
  username: "<EMAIL>", // Replace with your test user email/username
  password: "testpassword123", // Replace with your test user password
};

let AUTH_TOKEN = null;

async function login() {
  console.log("🔐 Logging in to get authentication token...");

  try {
    const response = await fetch(`${BASE_URL}/api/users/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(TEST_CREDENTIALS),
    });

    if (response.ok) {
      const data = await response.json();
      AUTH_TOKEN = data.token;
      console.log("✅ Login successful! Token obtained.");
      return true;
    } else {
      const error = await response.json();
      console.log("❌ Login failed:", error.message);
      return false;
    }
  } catch (error) {
    console.log("❌ Login error:", error.message);
    return false;
  }
}

function getHeaders() {
  return {
    "Content-Type": "application/json",
    Authorization: `Bearer ${AUTH_TOKEN}`,
  };
}

async function testAPI() {
  console.log("🚀 Testing Backend API Implementation...\n");

  // First, login to get a token
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log("❌ Cannot proceed without authentication. Please check your credentials.");
    return;
  }

  try {
    // Test 1: Create a DATE custom field
    console.log("📅 Test 1: Creating DATE custom field...");
    const dateFieldResponse = await fetch(`${BASE_URL}/api/workitems/custom-fields/`, {
      method: "POST",
      headers: getHeaders(),
      body: JSON.stringify({
        name: "Due Date",
        field_type: "DATE",
        is_required: false,
        sort_order: 1,
      }),
    });

    if (dateFieldResponse.ok) {
      const dateField = await dateFieldResponse.json();
      console.log("✅ DATE field created successfully:", dateField.name);
    } else {
      const error = await dateFieldResponse.json();
      console.log("❌ Failed to create DATE field:", error.message);
    }

    // Test 2: Create a WEEK custom field
    console.log("\n📅 Test 2: Creating WEEK custom field...");
    const weekFieldResponse = await fetch(`${BASE_URL}/api/workitems/custom-fields/`, {
      method: "POST",
      headers: getHeaders(),
      body: JSON.stringify({
        name: "Target Week",
        field_type: "WEEK",
        is_required: false,
        sort_order: 2,
      }),
    });

    if (weekFieldResponse.ok) {
      const weekField = await weekFieldResponse.json();
      console.log("✅ WEEK field created successfully:", weekField.name);
    } else {
      const error = await weekFieldResponse.json();
      console.log("❌ Failed to create WEEK field:", error.message);
    }

    // Test 3: Set week_starts_on preference to Monday
    console.log("\n⚙️ Test 3: Setting week_starts_on preference to Monday...");
    const preferencesResponse = await fetch(`${BASE_URL}/api/users/me/preferences/`, {
      method: "PATCH",
      headers: getHeaders(),
      body: JSON.stringify({
        week_starts_on: "Monday",
      }),
    });

    if (preferencesResponse.ok) {
      const preferences = await preferencesResponse.json();
      console.log("✅ Preferences updated successfully. week_starts_on:", preferences.week_starts_on);
    } else {
      const error = await preferencesResponse.json();
      console.log("❌ Failed to update preferences:", error.message);
    }

    // Test 4: Get preferences to verify week_starts_on
    console.log("\n📋 Test 4: Getting preferences to verify week_starts_on...");
    const getPreferencesResponse = await fetch(`${BASE_URL}/api/users/me/preferences/`, {
      method: "GET",
      headers: getHeaders(),
    });

    if (getPreferencesResponse.ok) {
      const preferences = await getPreferencesResponse.json();
      console.log("✅ Retrieved preferences. week_starts_on:", preferences.week_starts_on);
    } else {
      const error = await getPreferencesResponse.json();
      console.log("❌ Failed to get preferences:", error.message);
    }

    // Test 5: Test invalid week_starts_on value
    console.log("\n❌ Test 5: Testing invalid week_starts_on value...");
    const invalidPreferencesResponse = await fetch(`${BASE_URL}/api/users/me/preferences/`, {
      method: "PATCH",
      headers: getHeaders(),
      body: JSON.stringify({
        week_starts_on: "Tuesday",
      }),
    });

    if (!invalidPreferencesResponse.ok) {
      const error = await invalidPreferencesResponse.json();
      console.log("✅ Validation working correctly. Error:", error.message);
    } else {
      console.log("❌ Validation failed - invalid value was accepted");
    }
  } catch (error) {
    console.error("❌ Test failed with error:", error.message);
  }
}

// Instructions for running the test
console.log(`
📋 Instructions for Testing:

1. Make sure the development server is running (npm run dev)
2. Update the TEST_CREDENTIALS object with valid login credentials
3. Run this script with: node test-backend-api.js

Note: You can also test these endpoints using Postman or Insomnia with the following requests:

POST /api/workitems/custom-fields/
{
  "name": "Due Date",
  "field_type": "DATE",
  "is_required": false
}

POST /api/workitems/custom-fields/
{
  "name": "Target Week",
  "field_type": "WEEK",
  "is_required": false
}

PATCH /api/users/me/preferences/
{
  "week_starts_on": "Monday"
}
`);

// Run the tests automatically
testAPI();
